### 描述

批量删除Pod (版本：v3.12.1+，权限：容器Pod删除)

### 输入参数

| 参数名称 | 参数类型         | 必选 | 描述                             |
|------|--------------|----|--------------------------------|
| data | object array | 是  | 要删除的pod信息数组，data里所有pod总和最多200条 |

#### data

| 参数名称      | 参数类型      | 必选 | 描述                                    |
|-----------|-----------|----|---------------------------------------|
| bk_biz_id | int       | 是  | 业务ID                                  |
| ids       | int array | 是  | 要删除的pod的cc ID数组，data里所有pod的ID总和最多200条 |

### 调用示例

```json
{
  "data": [
    {
      "bk_biz_id": 123,
      "ids": [
        5,
        6
      ]
    }
  ]
}
```

### 响应示例

```json
{
  "result": true,
  "code": 0,
  "message": "",
  "permission": null,
}
```

### 响应参数说明

| 参数名称       | 参数类型   | 描述                         |
|------------|--------|----------------------------|
| result     | bool   | 请求成功与否。true:请求成功；false请求失败 |
| code       | int    | 错误编码。 0表示success，>0表示失败错误  |
| message    | string | 请求失败返回的错误信息                |
| permission | object | 权限信息                       |

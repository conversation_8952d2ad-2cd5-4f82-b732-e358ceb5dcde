### 描述

查询同步实例id规则字段状态 (版本：v3.14.1，权限：无)

### 输入参数

| 参数名称    | 参数类型   | 必选 | 描述     |
|---------|--------|----|---------|
| task_id | string | 是  | 任务id         |

### 调用示例

```json
{
    "task_id": "111"
}
```

### 响应示例

```json
{
    "result": true,
    "code": 0,
    "message": "",
    "permission": null,
    "data": {
      "status": "finished"
    }
}
```

### 响应参数说明

| 参数名称       | 参数类型   | 描述                         |
|------------|--------|----------------------------|
| result     | bool   | 请求成功与否。true:请求成功；false请求失败 |
| code       | int    | 错误编码。 0表示success，>0表示失败错误  |
| message    | string | 请求失败返回的错误信息                |
| permission | object | 权限信息                       |
| data       | object | 请求返回的数据                    |

#### data

| 参数名称       | 参数类型   | 描述                                                                                 |
|------------|--------|------------------------------------------------------------------------------------|
| status     | string | 任务执行状态，状态有"new"(新建),"waiting"(等待执行),"executing"(执行中),"finished"(已完成),"failure"(失败) |

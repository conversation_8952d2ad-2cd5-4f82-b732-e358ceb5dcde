问题描述
===========
<!-- 这里写问题描述 -->


重现方法
================
<!-- 列出如何重现的方法或操作步骤 -->


<!-- **重要提醒**:  请优先尝试最新发布的版本 (发布清单： https://github.com/TencentBlueKing/bk-cmdb/releases), 如果问题不能在最新发布的版本里重现，说明此问题已经被修复。 -->


关键信息
=========

<!--  **重要提醒**: 这些关键信息会辅助我们快速定位问题。 -->

请提供以下信息:

 - [x] bk-cmdb   版本: 
 <!-- 示例： ./cmdb/cmdb-apiserver --version 输出的内容:
Version  : oc-19.01.30
Tag      : 
Branch   : v3.3.x
BuildTime: 2019-01-30T11:29:27+0800
GitHash  : a99aabedf7a369387423ad0ab013730c88f4a7b6
RunMode  : product
 -->
 - [ ] Redis     版本: <!-- `示例： 3.2.11`  -->
 - [ ] MongoDB   版本: <!-- `示例： 2.8.0` -->
 - [ ] ZooKeeper 版本: <!-- `示例： 3.4.11` -->
 - [ ] 操作系统      : <!-- `示例： Centos 5 (x64>` -->
 - [ ] bk-cmdb 异常日志

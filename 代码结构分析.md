# 蓝鲸CMDB代码结构分析

## 📋 目录
- [项目概述](#项目概述)
- [整体架构](#整体架构)
- [核心目录结构](#核心目录结构)
- [代码阅读规划](#代码阅读规划)
- [二次开发指南](#二次开发指南)
- [实践建议](#实践建议)

## 🎯 项目概述

蓝鲸CMDB是一个大型的微服务架构项目，采用Go语言后端 + Vue.js前端的技术栈。项目遵循领域驱动设计（DDD）原则，通过多个独立的服务器组件协同工作。

### 技术栈
- **后端**: Go 1.19+, MongoDB, Redis, ZooKeeper
- **前端**: Vue.js 2.x, Element UI, Webpack
- **架构**: 微服务架构, RESTful API
- **存储**: MongoDB (主存储), Redis (缓存), Elasticsearch (搜索)

## 🏗️ 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Mobile App    │    │  Third Party    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      API Gateway         │
                    │    (cmdb_apiserver)      │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────▼───────┐    ┌─────────▼─────────┐    ┌─────────▼─────────┐
│  Web Server   │    │   Topo Server     │    │   Host Server     │
│(cmdb_webserver)│    │(cmdb_toposerver)  │    │(cmdb_hostserver)  │
└───────────────┘    └───────────────────┘    └───────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Core Services         │
                    │  (admin, auth, proc...)   │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      Data Layer          │
                    │  (MongoDB, Redis, ES)    │
                    └───────────────────────────┘
```

## 📁 核心目录结构

### 根目录结构
```
bk-cmdb/
├── src/                          # 核心源码目录
│   ├── scene_server/            # 场景服务器（核心业务逻辑）
│   ├── apiserver/               # API网关服务
│   ├── web_server/              # Web服务器
│   ├── source_controller/       # 数据源控制器
│   ├── common/                  # 公共库和常量
│   ├── storage/                 # 存储层抽象
│   ├── apimachinery/           # API客户端
│   └── ui/                     # 前端Vue.js代码
├── docs/                        # 文档
├── scripts/                     # 部署和初始化脚本
├── pkg/                        # 工具包
└── resources/                   # 资源文件
```

### 场景服务器详细结构
```
src/scene_server/
├── admin_server/               # 管理服务器（数据库升级、系统配置）
├── topo_server/               # 拓扑服务器（业务拓扑、模型管理）⭐
├── host_server/               # 主机服务器（主机管理）⭐
├── proc_server/               # 进程服务器（进程管理）
├── auth_server/               # 权限服务器（权限控制）
├── event_server/              # 事件服务器（事件处理）
├── datacollection/            # 数据采集服务
├── operation_server/          # 运营服务器（统计分析）
├── task_server/               # 任务服务器（异步任务）
├── cloud_server/              # 云服务器（云资源管理）
└── synchronize_server/        # 同步服务器（数据同步）
```

### 前端项目结构
```
src/ui/
├── src/
│   ├── views/                 # 页面组件
│   │   ├── business-topology/ # 业务拓扑页面⭐
│   │   ├── model-manage/      # 模型管理页面⭐
│   │   ├── resource/          # 主机管理页面⭐
│   │   └── ...
│   ├── components/            # 公共组件
│   ├── service/              # API服务层
│   ├── store/                # Vuex状态管理
│   ├── router/               # 路由配置
│   └── utils/                # 工具函数
├── static/                   # 静态资源
└── package.json             # 依赖配置
```

## 📚 代码阅读规划

### 🎯 第一阶段：基础架构理解（1-2周）

#### Week 1: 项目整体架构
**目标**: 理解项目的整体架构和设计理念

**必读文件**:
```bash
# 文档
docs/overview/architecture.md
docs/overview/design.md
docs/overview/code_framework.md

# 核心配置
scripts/init.py                 # 项目初始化脚本
go.mod                          # Go模块依赖
src/ui/package.json             # 前端依赖
```

**学习重点**:
- 微服务架构设计
- 各服务器组件职责
- 数据流向和交互方式

#### Week 2: 核心常量和数据结构
**目标**: 掌握项目的核心数据模型和常量定义

**必读文件**:
```bash
# 核心常量
src/common/definitions.go       # 系统常量定义
src/common/tablenames.go        # 数据库表名常量

# 数据结构
src/common/metadata/common.go   # 通用数据结构
src/common/metadata/object.go   # 模型相关结构
src/common/metadata/inst.go     # 实例相关结构
src/common/metadata/host.go     # 主机相关结构
```

**学习重点**:
- CMDB核心概念（业务、集群、模块、主机）
- 数据模型设计
- API请求/响应结构

### 🏗️ 第二阶段：存储层和核心服务（2-3周）

#### Week 3: 存储层理解
**目标**: 理解数据访问层的设计和实现

**必读文件**:
```bash
# 存储抽象层
src/storage/dal/dal.go          # 数据访问层接口定义
src/storage/dal/mongo/          # MongoDB实现
src/storage/dal/redis/          # Redis实现

# 数据库设计
docs/db/README.md               # 数据库表结构说明
```

**学习重点**:
- 数据访问层抽象设计
- MongoDB集合设计
- 缓存策略

#### Week 4: 拓扑服务器（最重要）
**目标**: 深入理解业务拓扑和模型管理的核心逻辑

**必读文件**:
```bash
# 服务入口
src/scene_server/topo_server/topo.go
src/scene_server/topo_server/app/server.go

# 核心服务层
src/scene_server/topo_server/service/service.go
src/scene_server/topo_server/service/inst_business.go    # 业务实例管理
src/scene_server/topo_server/service/object.go          # 模型管理
src/scene_server/topo_server/service/inst.go            # 通用实例管理

# 业务逻辑层
src/scene_server/topo_server/logics/                    # 业务逻辑实现
```

**学习重点**:
- 业务拓扑结构管理
- 模型定义和实例化
- API路由和处理逻辑

#### Week 5: 主机服务器
**目标**: 理解主机管理的核心功能

**必读文件**:
```bash
# 主机服务
src/scene_server/host_server/host.go
src/scene_server/host_server/service/               # 主机相关API
src/scene_server/host_server/logics/                # 主机业务逻辑
```

**学习重点**:
- 主机生命周期管理
- 主机与业务拓扑的关联
- 主机属性管理

### 🌐 第三阶段：前端架构（1-2周）

#### Week 6: Vue.js前端基础
**目标**: 理解前端项目的架构和核心机制

**必读文件**:
```bash
# 项目配置
src/ui/src/main.js              # 应用入口
src/ui/src/router/              # 路由配置
src/ui/src/store/               # Vuex状态管理

# API服务层
src/ui/src/service/             # 业务API封装
src/ui/src/api/                 # HTTP客户端
```

**学习重点**:
- Vue.js项目结构
- 状态管理模式
- API调用机制

#### Week 7: 核心页面组件
**目标**: 理解主要功能页面的实现

**必读文件**:
```bash
# 业务拓扑
src/ui/src/views/business-topology/index.vue
src/ui/src/views/business-topology/children/topology-tree.vue

# 模型管理
src/ui/src/views/model-manage/index.vue

# 主机管理
src/ui/src/views/resource/index.vue
```

**学习重点**:
- 组件化开发模式
- 前后端数据交互
- 用户界面设计模式

## 🚀 二次开发指南

### 新手友好的入手点

#### 1. 前端组件开发 ⭐⭐
**适合**: 前端开发者
**位置**: `src/ui/src/components/`
**示例项目**:
- 添加新的表单组件
- 创建自定义图表组件
- 扩展现有UI组件功能

**开发步骤**:
```bash
# 1. 创建组件
src/ui/src/components/my-component/index.vue

# 2. 注册组件
src/ui/src/components/index.js

# 3. 在页面中使用
<my-component :data="componentData" />
```

#### 2. 页面功能扩展 ⭐⭐⭐
**适合**: 全栈开发者
**位置**: `src/ui/src/views/`
**示例项目**:
- 在业务拓扑页面添加批量操作功能
- 为主机管理添加自定义筛选器
- 创建新的管理页面

#### 3. API接口扩展 ⭐⭐⭐⭐
**适合**: 后端开发者
**位置**: `src/scene_server/*/service/`
**示例项目**:
- 添加新的查询接口
- 扩展现有API的功能
- 优化API性能

**开发步骤**:
```go
// 1. 在service层添加新方法
func (s *Service) NewAPI(ctx *rest.Contexts) {
    // 实现逻辑
}

// 2. 注册路由
utility.AddHandler(rest.Action{
    Verb: http.MethodPost, 
    Path: "/new/api", 
    Handler: s.NewAPI
})
```

### 进阶开发入手点

#### 1. 新模型类型支持 ⭐⭐⭐⭐⭐
**位置**: `src/scene_server/topo_server/`
**示例**: 添加网络设备模型支持

#### 2. 数据库升级脚本 ⭐⭐⭐⭐
**位置**: `src/scene_server/admin_server/upgrader/`
**示例**: 添加新的数据迁移逻辑

#### 3. 新的微服务 ⭐⭐⭐⭐⭐
**位置**: `src/scene_server/`
**示例**: 创建专门的监控服务

## 🛠️ 实践建议

### 开发环境搭建

#### 后端开发环境
```bash
# 1. 安装Go 1.19+
go version

# 2. 编译特定服务
cd src/scene_server/topo_server
go build
./topo_server --help

# 3. 本地调试
go run topo.go --config=configures/topo.yaml
```

#### 前端开发环境
```bash
# 1. 安装Node.js 14+
node --version

# 2. 安装依赖
cd src/ui
npm install

# 3. 启动开发服务器
npm run dev
```

### 调试技巧

#### 后端调试
```go
// 添加日志
blog.Infof("debug info: %+v", data)

// 错误处理
if err != nil {
    blog.Errorf("operation failed: %v", err)
    ctx.RespAutoError(err)
    return
}
```

#### 前端调试
```javascript
// 控制台调试
console.log('debug data:', data)

// Vue开发者工具
this.$log.debug('component data:', this.componentData)
```

### 测试策略

#### 单元测试
```bash
# Go测试
go test ./src/common/...
go test ./src/scene_server/topo_server/...

# 前端测试
cd src/ui && npm test
```

#### 集成测试
- 使用Postman测试API接口
- 浏览器开发者工具调试前端交互
- 数据库直接查询验证数据正确性

### 代码规范

#### Go代码规范
- 遵循Go官方代码规范
- 使用`gofmt`格式化代码
- 添加必要的注释和文档

#### 前端代码规范
- 遵循Vue.js风格指南
- 使用ESLint检查代码质量
- 组件命名采用PascalCase

## 📖 持续学习资源

1. **官方文档**: `docs/` 目录下的所有文档
2. **代码注释**: 重点关注接口定义和核心逻辑
3. **Git历史**: 查看功能演进和重要变更
4. **社区资源**: GitHub Issues和Discussions
5. **相关技术**: Go、Vue.js、MongoDB官方文档

---

**注意**: 这是一个大型项目，建议循序渐进地学习，先理解整体架构，再深入具体模块。在二次开发时，建议先从小功能开始，逐步熟悉项目的开发模式和代码风格。

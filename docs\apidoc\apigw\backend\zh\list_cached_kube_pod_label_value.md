### 描述

获取缓存的Pod的标签键对应的值列表 (版本：v3.13.5+，权限：业务访问)

**注意：**
- 该接口会返回指定业务下全量Pod中指定标签键对应的去重后的标签值列表数据。
- 该接口为缓存接口，默认全量缓存刷新时间为一天一次。
- 如果Pod信息发生变化，会通过事件机制实时刷新该Pod中标签值对应的缓存数据。
- 该接口仅供前端页面根据标签键选择标签值的下拉列表使用，不建议在其他场景使用。如果用于其他场景导致异常情况，后果自负。

### 输入参数

| 参数名称      | 参数类型   | 必选 | 描述   |
|-----------|--------|----|------|
| bk_biz_id | int    | 是  | 业务ID |
| key       | string | 是  | 标签键  |

### 调用示例

```json
{
  "bk_biz_id": 3,
  "key": "key1"
}
```

### 响应示例

```json
{
  "result": true,
  "code": 0,
  "message": "success",
  "permission": null,
  "data": {
    "values": [
      "value1",
      "value2"
    ]
  }
}
```

### 响应参数说明

| 参数名称       | 参数类型   | 描述                         |
|------------|--------|----------------------------|
| result     | bool   | 请求成功与否。true:请求成功；false请求失败 |
| code       | int    | 错误编码。 0表示success，>0表示失败错误  |
| message    | string | 请求失败返回的错误信息                |
| permission | object | 权限信息                       |
| data       | object | 请求返回的数据                    |

#### data

| 参数名称   | 参数类型         | 描述                           |
|--------|--------------|------------------------------|
| values | string array | 业务下全量Pod中指定标签键对应的去重后的标签值列表数据 |

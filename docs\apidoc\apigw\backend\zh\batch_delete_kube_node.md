### 描述

删除容器节点(v3.12.1+，权限：容器节点的删除权限)

### 输入参数

| 参数名称      | 参数类型  | 必选 | 描述          |
|-----------|-------|----|-------------|
| bk_biz_id | int   | 是  | 容器节点所属业务ID  |
| ids       | array | 是  | 需要删除节点的ID列表 |

**注意：**

- 用户需要保证节点下没有关联资源(如：pod)，否则删除失败。
- 一次性删除节点数量不超过100个。

### 调用示例

```json
{
  "bk_biz_id": 2,
  "ids": [
    1,
    2
  ]
}
```

### 响应示例

```json
{
  "result": true,
  "code": 0,
  "message": "",
  "permission": null,
  "data": null,
}
```

### 响应参数说明

| 参数名称       | 参数类型   | 描述                         |
|------------|--------|----------------------------|
| result     | bool   | 请求成功与否。true:请求成功；false请求失败 |
| code       | int    | 错误编码。 0表示success，>0表示失败错误  |
| message    | string | 请求失败返回的错误信息                |
| permission | object | 权限信息                       |
| data       | object | 无数据返回                      |

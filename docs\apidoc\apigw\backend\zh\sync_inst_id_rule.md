### 描述

当模型实例的id规则字段为空时，通过该接口异步刷新id规则字段值到模型实例的字段 (版本：v3.14.1，权限：对应实例的编辑权限)

### 输入参数

| 参数名称      | 参数类型   | 必选 | 描述                                                  |
|-----------|--------|----|-----------------------------------------------------|
| bk_obj_id      | string | 是  | 模型ID         |

### 调用示例

```json
{
    "bk_obj_id": "host"
}
```

### 响应示例

```json
{
    "result": true,
    "code": 0,
    "message": "",
    "permission": null,
    "data": {
      "task_id": "111"
    }
}
```

### 响应参数说明

| 参数名称       | 参数类型   | 描述                         |
|------------|--------|----------------------------|
| result     | bool   | 请求成功与否。true:请求成功；false请求失败 |
| code       | int    | 错误编码。 0表示success，>0表示失败错误  |
| message    | string | 请求失败返回的错误信息                |
| permission | object | 权限信息                       |
| data       | object | 请求返回的数据                    |

#### data

| 参数名称       | 参数类型   | 描述   |
|------------|--------|------|
| task_id     | string | 任务id |

### Description

Listen to events generated by changes in system resources (Version: v3.8 and above, Permissions: Different permissions
based on the type of resource being listened to, including: host event listening, host relationship event listening,
business event listening, set event listening, module data listening, process data listening, model instance event
listening, custom topology level event listening, instance association event listening, business set event listening,
control area event listening, container cluster event listening, container node event listening, container namespace
event listening, container workload event listening, container Pod event listening, project event listening)

**The main features of this watch function include:**

- Provide users with highly available data change watch services within a limited time (currently 3 hours, subject to
  change, do not rely on this time).
- Within a limited time, users can use the cursor of their last event to trace events or fetch data, suitable for
  abnormal data tracing or data supplementation due to system changes.
- Support tracing changes in data based on a specific time point, support tracing changes based on a cursor, and support
  watching data changes from the current time point.
- Support the ability to watch events based on event types, including add, update, and delete. The event contains the
  full data.
- Support the ability to watch changes in host and host relationship data.
- Adopt a short-long chain design. When a user watches events using a cursor, if there are no events, the session
  connection will be maintained. If there are events within 20 seconds, the events will be directly pushed back,
  avoiding continuous user requests and ensuring that users can get change data in a timely manner.
- Support batch event watching to improve system throughput.
- Support customizing the fields of event data to be monitored, meeting the lightweight watch needs of users.

### Parameters

| Name                | Type             | Required              | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
|---------------------|------------------|-----------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| bk_event_types      | array of strings | No                    | Event types, if specified, only pay attention to events of this type. Possible values: create (new)/update (update)/delete (delete). If used, only pay attention to events of adding resources. If not filled, pay attention to all events.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| bk_fields           | array of strings | Depending on the case | List of fields that need to be returned in the event. Currently, for listening to host resources, this field is required and cannot be empty. It can be empty for host relationships. If empty, all fields are returned by default.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| bk_start_from       | Int64            | No                    | The start time of listening to events. This value is the number of seconds from UTC 1970-01-01 00:00:00 to the total seconds of the time you want to watch.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| bk_cursor           | string           | No                    | The cursor of listening to events, representing the event address to start or continue watching. The system will return the next or a batch of events of this cursor.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| bk_resource         | string           | Yes                   | The type of resource to be listened to, with possible values: host, host_relation, biz, set, module, process, object_instance, mainline_instance, biz_set, biz_set_relation, plat, project. Among them, host represents the details event of the host, host_relation represents the relationship event of the host, biz represents the details event of the business, set represents the details event of the set, module represents the details event of the module, process represents the details event of the process, object_instance represents the event of the general model instance, mainline_instance represents the event of the mainline model instance, biz_set represents the event of the business set, biz_set_relation represents the relationship event of the business set and the business, plat represents the event of the control area, project represents the event of the project. |
| bk_supplier_account | string           | Yes                   | Developer account.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| bk_filter           | object           | No                    | Filter conditions.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

**Note: The biz_set_relation event will be triggered when the "bk_scope" field of the business set is added, deleted, or
updated, and when the relationship changes related to the business set are added, deleted, or updated. The event type (
bk_event_type) of all business set relationship events is update, and the event details will return the ID of the
business set that has changed and the list of all business IDs included in the business set. When the event is triggered
by the deletion event of the business set, the list of business IDs in the event details is empty.**

#### bk_filter

| Name            | Type   | Required | Description                                                                                                                                                                                                     |
|-----------------|--------|----------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| bk_sub_resource | string | No       | The type of the subordinate resource to be listened to, which is only supported when bk_resource is object_instance or mainline_instance, representing the bk_obj_id of the model that needs to be listened to. |

### Request Example

Host:

```json
{
    "bk_event_types": ["create", "update", "delete"],
    "bk_fields": ["bk_host_innerip", "bk_mac"],
    "bk_start_from": ***********,
    "bk_cursor": "MQ0yDTE1ODkyMDcyODENMQ01ZWI3ZWZjNTBiOTA5ZTYyMGFmYWQzZGY=",
    "bk_resource": "host"
}
```

General model instance:

```json
{
    "bk_event_types": [],
    "bk_fields": ["bk_inst_id", "bk_inst_name"],
    "bk_start_from": ***********,
    "bk_cursor": "MQ0yDTE1ODkyMDcyODENMQ01ZWI3ZWZjNTBiOTA5ZTYyMGFmYWQzZGY=",
    "bk_resource": "object_instance",
    "bk_filter": {
        "bk_sub_resource": "xxx"
    },
}
```

## Return Parameters

```json
{
    "result": true,
    "code": 0,
    "message": "success",
    "permission": null,
    "request_id": "e43da4ef221746868dc4c837d36f3807",
    "data": {
        "bk_watched": true,
        "bk_events": [
            {
                "bk_cursor": "MQ0yDTE1ODkyMDcyODENMQ01ZWI3ZWZjNTBiOTA5ZTYyMGFmYWQzZGY=",
                "bk_resource": "host",
                "bk_event_type": "update",
                "bk_detail": {
                    "bk_cpu": 2
                }
            },
            {
                "bk_cursor": "MQ0yDTE1ODkzNDExMDcNMQ01ZWI3ZWZjNTBiOTA5ZTYyMGFmYWQzZGY=",
                "bk_resource": "host",
                "bk_event_type": "update",
                "bk_detail": {
                    "bk_cpu": 2
                }
            }
        ]
    }
}
```

### Response Example

#### response

| Field      | Type   | Description                                                                                  |
|------------|--------|----------------------------------------------------------------------------------------------|
| result     | bool   | Whether the request is successful. true: successful; false: failed                           |
| code       | int    | Error code. 0 indicates success, >0 indicates failed error                                   |
| message    | string | Error message returned in case of failure                                                    |
| permission | object | Permission information                                                                       |
| request_id | string | Request chain id                                                                             |
| data       | Array  | Event data details, an ordered array, and the events at the end of the array are new events. |

- data Data Description

| Field      | Type  | Description                                                                                                            |
|------------|-------|------------------------------------------------------------------------------------------------------------------------|
| bk_watched | bool  | Whether events have been listened to. true: listened to events; false: did not listen to events                        |
| bk_events  | array | List of event details, with a maximum length of 200, and the length may be adjusted later, do not rely on this length. |

- bk_events Data Description

| Field         | Type   | Description                                                                                                                              |
|---------------|--------|------------------------------------------------------------------------------------------------------------------------------------------|
| bk_cursor     | string | Represents the cursor value of the current resource event. The calling party can use this cursor to get the next event after this event. |
| bk_resource   | string | The resource type corresponding to this event.                                                                                           |
| bk_event_type | string | The event type corresponding to this event, with possible values: create (new)/update (update)/delete (delete).                          |
| bk_detail     | object | The detailed data of the resource corresponding to this event, and the details are different for different resources.                    |

#### host_relation resource bk_detail field data example:

```json
{
	"bk_biz_id" : 1,
	"bk_host_id" : 2,
	"bk_module_id" : 3,
	"bk_set_id" : 4,
	"bk_supplier_account" : "0"
}
```

#### host resource bk_detail field data example:

```json
{
	"bk_host_name" : "hostname",
	"bk_mem" : null,
	"bk_cloud_id" : 0,
	"operator" : "user",
	"bk_cpu" : null,
	"bk_mac" : "",
	"bk_host_innerip" : "***********",	
        "bk_supplier_account" : "0",
	....
}
```

#### biz_set_relation resource bk_detail field data example:

```json
{
	"bk_biz_set_id": 1,
	"bk_biz_ids": [1 ,2, 3]
}
```

- biz_set_relation resource bk_detail data description

| Field         | Type      | Description                                                                                             |
|---------------|-----------|---------------------------------------------------------------------------------------------------------|
| bk_biz_set_id | int       | The ID of the business set where the relationship between the business set and the business has changed |
| bk_biz_ids    | int array | List of IDs of all businesses included in the business set                                              |

### Response Parameters
